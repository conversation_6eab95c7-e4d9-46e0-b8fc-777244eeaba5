#!/usr/bin/env python3
"""
测试管理员模态框修复
验证激进修复方法是否正确应用
"""

import os
import sys

def check_aggressive_css_fix():
    """检查激进的CSS修复"""
    base_file = 'templates/admin/base.html'
    
    if not os.path.exists(base_file):
        print("❌ 管理员基础模板文件不存在")
        return False
    
    with open(base_file, 'r', encoding='utf-8') as f:
        base_content = f.read()
    
    aggressive_checks = [
        # 激进修复标记
        ('🚨 强制修复模态框交互问题', '激进修复标记'),
        ('pointer-events: auto !important', '强制启用指针事件'),
        ('user-select: auto !important', '强制启用用户选择'),
        
        # 极高z-index
        ('z-index: 9999 !important', '模态框极高z-index'),
        ('z-index: 9998 !important', '背景极高z-index'),
        ('z-index: 10000 !important', '对话框极高z-index'),
        ('z-index: 10001 !important', '内容极高z-index'),
        ('z-index: 10002 !important', '头部/主体/底部z-index'),
        ('z-index: 10003 !important', '表单元素z-index'),
        ('z-index: 10004 !important', '按钮/文件输入z-index'),
        
        # 强制样式
        ('position: fixed !important', '强制固定定位'),
        ('width: 100% !important', '强制全宽'),
        ('height: 100% !important', '强制全高'),
        ('cursor: pointer !important', '强制指针光标'),
        ('opacity: 1 !important', '强制不透明'),
        ('visibility: visible !important', '强制可见'),
        
        # 移除干扰
        ('display: none !important', '移除伪元素'),
        ('outline: 0 !important', '移除轮廓'),
        
        # 浏览器兼容
        ('-webkit-user-select: auto !important', 'WebKit用户选择'),
        ('-moz-user-select: auto !important', 'Mozilla用户选择'),
        ('-ms-user-select: auto !important', 'IE用户选择'),
    ]
    
    print("🚨 检查激进CSS修复...")
    all_passed = True
    
    for check, description in aggressive_checks:
        if check in base_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def check_aggressive_js_fix():
    """检查激进的JavaScript修复"""
    requests_file = 'templates/admin/requests.html'
    
    if not os.path.exists(requests_file):
        print("❌ 管理员请求页面文件不存在")
        return False
    
    with open(requests_file, 'r', encoding='utf-8') as f:
        requests_content = f.read()
    
    js_checks = [
        # 全局函数
        ('window.forceFixModalInteraction', '全局强制修复函数'),
        
        # 激进修复标记
        ('🚨 强制修复模态框交互', 'JavaScript激进修复标记'),
        ('最激进的方法', '激进方法标记'),
        
        # 强制样式设置
        ('element.style.pointerEvents = \'auto\'', '强制启用指针事件'),
        ('element.style.userSelect = \'auto\'', '强制启用用户选择'),
        ('element.style.webkitUserSelect = \'auto\'', 'WebKit用户选择'),
        ('element.style.mozUserSelect = \'auto\'', 'Mozilla用户选择'),
        ('element.style.msUserSelect = \'auto\'', 'IE用户选择'),
        
        # z-index设置
        ('(10000 + index).toString()', '动态z-index计算'),
        ('style.zIndex = \'9999\'', '模态框z-index'),
        ('style.zIndex = \'10100\'', '最高z-index'),
        
        # 事件监听
        ('addEventListener(\'click\'', '点击事件监听'),
        ('e.stopPropagation()', '阻止事件冒泡'),
        
        # 调试日志
        ('console.log(\'🔧 开始修复', '修复开始日志'),
        ('console.log(\'🎯 正在强制修复', '强制修复日志'),
        ('console.log(\'📁 文件输入被点击', '文件输入点击日志'),
        ('console.log(\'🔘 按钮被点击', '按钮点击日志'),
        ('console.log(\'✅ 模态框修复完成', '修复完成日志'),
        
        # 特殊处理
        ('tagName.match(/^(INPUT|TEXTAREA|BUTTON', '交互元素匹配'),
        ('input[type="file"]', '文件输入选择器'),
        ('setTimeout(() => forceFixModalInteraction', '延迟修复'),
        
        # 降级方案
        ('backdrop: false', '禁用背景'),
        ('modalElement.style.display = \'block\'', '直接显示'),
        ('classList.add(\'show\')', '添加显示类'),
        ('modal-open', '模态框打开类'),
    ]
    
    print("\n🔧 检查激进JavaScript修复...")
    all_passed = True
    
    for check, description in js_checks:
        if check in requests_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def check_modal_structure_integrity():
    """检查模态框结构完整性"""
    requests_file = 'templates/admin/requests.html'
    
    with open(requests_file, 'r', encoding='utf-8') as f:
        requests_content = f.read()
    
    structure_checks = [
        # 基础结构
        ('id="completeRequestModal"', '完成请求模态框ID'),
        ('class="modal fade"', '模态框基础类'),
        ('class="modal-dialog modal-lg"', '模态框对话框'),
        ('class="modal-content"', '模态框内容'),
        ('class="modal-header"', '模态框头部'),
        ('class="modal-body"', '模态框主体'),
        ('class="modal-footer"', '模态框底部'),
        
        # 表单结构
        ('id="completeRequestForm"', '完成请求表单'),
        ('enctype="multipart/form-data"', '表单编码'),
        ('id="reportFile"', '报告文件输入'),
        ('id="analysisFile"', '分析文件输入'),
        ('type="file"', '文件输入类型'),
        
        # 按钮结构
        ('data-bs-dismiss="modal"', '关闭按钮'),
        ('type="submit"', '提交按钮'),
        ('btn btn-success', '成功按钮样式'),
    ]
    
    print("\n🏗️  检查模态框结构完整性...")
    all_passed = True
    
    for check, description in structure_checks:
        if check in requests_content:
            print(f"  ✅ {description}: 完整")
        else:
            print(f"  ❌ {description}: 缺失")
            all_passed = False
    
    return all_passed


def analyze_fix_strategy():
    """分析修复策略"""
    print("\n🎯 激进修复策略分析...")
    
    strategies = [
        "🚨 极高Z-Index: 使用9999+的z-index确保最高层级",
        "🔧 强制样式: 使用!important覆盖所有可能的干扰样式",
        "🎯 全元素修复: 遍历所有子元素并强制设置交互属性",
        "📱 多重保险: DOM加载、模态框显示、延迟修复多重触发",
        "🔍 详细调试: 每个关键操作都有console.log输出",
        "🛡️  降级方案: Bootstrap失败时直接操作DOM显示模态框",
        "⚡ 事件监听: 为每个交互元素添加点击事件监听",
        "🌐 浏览器兼容: 处理WebKit、Mozilla、IE的用户选择属性",
        "📁 特殊处理: 文件输入和按钮有额外的修复逻辑",
        "🔄 实时修复: 模态框显示时实时应用修复",
    ]
    
    for strategy in strategies:
        print(f"  {strategy}")
    
    return True


def main():
    """主函数"""
    print("🚨 验证激进模态框修复")
    print("=" * 60)
    
    css_ok = check_aggressive_css_fix()
    js_ok = check_aggressive_js_fix()
    structure_ok = check_modal_structure_integrity()
    strategy_ok = analyze_fix_strategy()
    
    print("\n" + "=" * 60)
    print("📊 验证结果")
    print("=" * 60)
    
    if css_ok and js_ok and structure_ok and strategy_ok:
        print("🚨 激进模态框修复已完全部署！")
        print("\n🎯 修复特点:")
        print("  • 🚨 激进方法: 使用最强力的修复策略")
        print("  • 🔧 强制覆盖: 所有样式都使用!important")
        print("  • 🎯 全面修复: 每个元素都被强制设置")
        print("  • 📱 多重保险: 多个时机触发修复")
        print("  • 🔍 详细调试: 完整的调试日志系统")
        print("\n💡 测试建议:")
        print("  • 打开浏览器开发者工具查看详细日志")
        print("  • 检查模态框显示时的实时修复过程")
        print("  • 验证每个交互元素的点击响应")
        print("  • 测试文件上传和表单提交功能")
        print("\n🎉 如果这次还不能解决问题，可能需要检查:")
        print("  • 浏览器版本兼容性")
        print("  • Bootstrap版本冲突")
        print("  • 其他JavaScript库的干扰")
        print("  • 浏览器安全策略限制")
        return True
    else:
        print("❌ 激进修复部署不完整，请检查上述错误")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
