#!/usr/bin/env python3
"""
验证管理员模态框修复
检查CSS和JavaScript修复是否正确应用
"""

import os
import sys

def check_admin_base_css():
    """检查管理员基础模板的CSS修复"""
    base_file = 'templates/admin/base.html'
    
    if not os.path.exists(base_file):
        print("❌ 管理员基础模板文件不存在")
        return False
    
    with open(base_file, 'r', encoding='utf-8') as f:
        base_content = f.read()
    
    css_checks = [
        # 模态框z-index修复
        ('.modal', '模态框样式定义'),
        ('z-index: 1060 !important', '模态框z-index'),
        ('.modal-backdrop', '模态框背景样式'),
        ('z-index: 1055 !important', '模态框背景z-index'),
        
        # 模态框内容修复
        ('.modal-content', '模态框内容样式'),
        ('z-index: 1061 !important', '模态框内容z-index'),
        ('pointer-events: auto !important', '确保可点击'),
        ('backdrop-filter: blur(15px)', '模态框模糊效果'),
        
        # 表单元素修复
        ('.modal-body input', '模态框输入框'),
        ('.modal-body textarea', '模态框文本域'),
        ('.modal-body button', '模态框按钮'),
        ('z-index: 1062 !important', '表单元素z-index'),
        
        # 文件上传修复
        ('.form-control[type="file"]', '文件上传控件'),
        ('cursor: pointer !important', '文件上传光标'),
        
        # 模态框按钮修复
        ('.modal-footer .btn', '模态框底部按钮'),
    ]
    
    print("🎨 检查管理员基础CSS修复...")
    all_passed = True
    
    for check, description in css_checks:
        if check in base_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def check_requests_js_fix():
    """检查请求页面的JavaScript修复"""
    requests_file = 'templates/admin/requests.html'
    
    if not os.path.exists(requests_file):
        print("❌ 管理员请求页面文件不存在")
        return False
    
    with open(requests_file, 'r', encoding='utf-8') as f:
        requests_content = f.read()
    
    js_checks = [
        # DOM加载事件
        ("document.addEventListener('DOMContentLoaded'", 'DOM加载事件监听'),
        ('completeRequestModal', '完成请求模态框ID'),
        ("addEventListener('shown.bs.modal'", '模态框显示事件'),
        
        # 交互修复
        ('pointerEvents = \'auto\'', '启用指针事件'),
        ('style.zIndex = \'1061\'', '设置元素z-index'),
        ('style.cursor = \'pointer\'', '设置文件输入光标'),
        
        # 模态框配置
        ('backdrop: \'static\'', '静态背景配置'),
        ('keyboard: true', '键盘事件配置'),
        
        # 调试日志
        ('console.log(\'表单提交事件触发\')', '表单提交调试'),
        ('console.log(\'模态框已显示\')', '模态框显示调试'),
        
        # 事件处理
        ('querySelectorAll(\'input, textarea, button', '查询交互元素'),
        ('forEach(element =>', '遍历元素设置'),
        ('{ once: true }', '一次性事件监听'),
    ]
    
    print("\n🔧 检查请求页面JavaScript修复...")
    all_passed = True
    
    for check, description in js_checks:
        if check in requests_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def check_modal_structure():
    """检查模态框HTML结构"""
    requests_file = 'templates/admin/requests.html'
    
    with open(requests_file, 'r', encoding='utf-8') as f:
        requests_content = f.read()
    
    structure_checks = [
        # 模态框基础结构
        ('id="completeRequestModal"', '完成请求模态框ID'),
        ('class="modal fade"', '模态框基础类'),
        ('tabindex="-1"', '模态框tabindex'),
        ('class="modal-dialog modal-lg"', '模态框对话框'),
        
        # 表单结构
        ('id="completeRequestForm"', '完成请求表单ID'),
        ('enctype="multipart/form-data"', '表单编码类型'),
        ('type="file"', '文件输入类型'),
        ('accept=".md"', '报告文件类型限制'),
        ('accept=".html,.htm"', '分析文件类型限制'),
        
        # 按钮结构
        ('data-bs-dismiss="modal"', '关闭模态框按钮'),
        ('type="submit"', '提交按钮'),
        ('btn btn-success', '成功按钮样式'),
    ]
    
    print("\n🏗️  检查模态框HTML结构...")
    all_passed = True
    
    for check, description in structure_checks:
        if check in requests_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def analyze_fix_approach():
    """分析修复方法"""
    print("\n🔍 修复方法分析...")
    
    fix_approaches = [
        "✅ Z-Index层级: 设置模态框及其内容的正确z-index值",
        "✅ 指针事件: 确保所有交互元素的pointer-events为auto",
        "✅ 位置设置: 使用relative定位确保元素在正确层级",
        "✅ 事件监听: 在模态框显示后动态设置交互属性",
        "✅ 调试日志: 添加console.log帮助排查问题",
        "✅ 静态背景: 防止意外关闭模态框",
        "✅ 文件上传: 特别处理文件输入的光标和交互",
        "✅ 样式覆盖: 使用!important确保样式优先级",
    ]
    
    for approach in fix_approaches:
        print(f"  {approach}")
    
    return True


def main():
    """主函数"""
    print("🔍 验证管理员模态框修复")
    print("=" * 60)
    
    css_ok = check_admin_base_css()
    js_ok = check_requests_js_fix()
    structure_ok = check_modal_structure()
    analysis_ok = analyze_fix_approach()
    
    print("\n" + "=" * 60)
    print("📊 验证结果")
    print("=" * 60)
    
    if css_ok and js_ok and structure_ok and analysis_ok:
        print("✅ 管理员模态框修复完成！")
        print("\n🎯 修复的问题:")
        print("  • 🎨 CSS层级: 修复z-index冲突问题")
        print("  • 🖱️  指针事件: 确保所有元素可点击")
        print("  • 📁 文件上传: 修复文件选择器交互")
        print("  • 🔧 JavaScript: 动态设置交互属性")
        print("  • 🐛 调试支持: 添加日志帮助排查")
        print("\n💡 使用建议:")
        print("  • 打开浏览器开发者工具查看调试日志")
        print("  • 检查模态框显示时的z-index值")
        print("  • 确认所有表单元素都可以正常交互")
        print("  • 测试文件上传功能是否正常")
        print("\n🎉 现在管理员应该可以正常使用完成请求功能了！")
        return True
    else:
        print("❌ 部分修复未正确应用，请检查上述错误")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
