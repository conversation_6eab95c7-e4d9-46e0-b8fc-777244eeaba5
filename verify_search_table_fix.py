#!/usr/bin/env python3
"""
验证搜索框和表格样式修复
检查是否已应用高级配色方案
"""

import os
import sys

def check_search_section_styles():
    """检查搜索部分的样式修复"""
    html_file = 'templates/public/index.html'
    
    if not os.path.exists(html_file):
        print("❌ 首页模板文件不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    search_checks = [
        # 搜索卡片样式
        ('.search-section .search-card', '搜索卡片样式定义'),
        ('background: linear-gradient(135deg, rgba(255, 255, 255, 0.95)', '搜索卡片渐变背景'),
        ('backdrop-filter: blur(10px)', '搜索卡片模糊效果'),
        ('border: 1px solid rgba(148, 163, 184, 0.2)', '搜索卡片边框'),
        
        # 搜索表单样式
        ('.search-section .form-control', '搜索输入框样式'),
        ('background: rgba(255, 255, 255, 0.9)', '输入框背景'),
        ('color: #334155', '输入框文字颜色'),
        ('::placeholder', '占位符样式'),
        ('color: #64748b', '占位符颜色'),
        
        # 搜索按钮样式
        ('.search-section .btn-primary', '搜索按钮样式'),
        ('background: linear-gradient(135deg, rgba(99, 102, 241, 0.9)', '按钮渐变背景'),
        (':hover', '按钮悬停效果'),
        ('transform: translateY(-1px)', '按钮悬停上移'),
        
        # 搜索标题样式
        ('.search-section h3', '搜索标题样式'),
        ('color: #334155 !important', '标题颜色'),
    ]
    
    print("🔍 检查搜索部分样式修复...")
    all_passed = True
    
    for check, description in search_checks:
        if check in html_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def check_table_styles():
    """检查表格样式修复"""
    html_file = 'templates/public/index.html'
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    table_checks = [
        # 表格基础样式
        ('.table', '表格样式定义'),
        ('background: rgba(255, 255, 255, 0.95)', '表格背景'),
        ('backdrop-filter: blur(10px)', '表格模糊效果'),
        ('border-radius: 0.5rem', '表格圆角'),
        
        # 表头样式
        ('.table thead th', '表头样式'),
        ('background: linear-gradient(135deg, rgba(248, 250, 252, 0.95)', '表头渐变背景'),
        ('color: #334155', '表头文字颜色'),
        ('font-weight: 600', '表头字体粗细'),
        
        # 表格行样式
        ('.table tbody tr', '表格行样式'),
        ('.table tbody tr:hover', '表格行悬停'),
        ('transform: translateY(-1px)', '行悬停上移'),
        
        # 表格单元格样式
        ('.table tbody td', '表格单元格样式'),
        ('color: #475569', '单元格文字颜色'),
        ('border-bottom: 1px solid rgba(148, 163, 184, 0.1)', '单元格边框'),
        
        # 表格按钮样式
        ('.table .btn-outline-primary', '表格主要按钮'),
        ('.table .btn-outline-success', '表格成功按钮'),
        ('.table .btn-outline-secondary', '表格次要按钮'),
    ]
    
    print("\n📊 检查表格样式修复...")
    all_passed = True
    
    for check, description in table_checks:
        if check in html_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def check_card_and_modal_styles():
    """检查卡片和模态框样式"""
    html_file = 'templates/public/index.html'
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    card_modal_checks = [
        # 卡片样式
        ('.card', '卡片样式定义'),
        ('background: linear-gradient(135deg, rgba(255, 255, 255, 0.95)', '卡片渐变背景'),
        ('.card:hover', '卡片悬停效果'),
        
        # 模态框样式
        ('.modal-content', '模态框内容样式'),
        ('.modal-header', '模态框头部样式'),
        ('.modal-body .form-control', '模态框表单控件'),
        ('.modal-footer', '模态框底部样式'),
        
        # 模态框按钮
        ('.modal-footer .btn-secondary', '模态框次要按钮'),
        ('.modal-footer .btn-primary', '模态框主要按钮'),
    ]
    
    print("\n🎴 检查卡片和模态框样式...")
    all_passed = True
    
    for check, description in card_modal_checks:
        if check in html_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def analyze_color_consistency():
    """分析颜色一致性"""
    print("\n🎨 颜色一致性分析...")
    
    consistency_features = [
        "✅ 统一背景: 所有元素使用相同的渐变背景系统",
        "✅ 一致边框: 统一使用 rgba(148, 163, 184, 0.2) 边框色",
        "✅ 协调文字: #334155, #475569, #64748b 的灰色层次",
        "✅ 统一模糊: 全部使用 backdrop-filter 玻璃效果",
        "✅ 一致阴影: 统一的多层阴影系统",
        "✅ 协调交互: 统一的悬停和点击效果",
        "✅ 品牌色彩: 主要按钮使用统一的蓝紫色系",
        "✅ 层次清晰: 从白色到浅灰的渐进层次",
    ]
    
    for feature in consistency_features:
        print(f"  {feature}")
    
    return True


def main():
    """主函数"""
    print("🔍 验证搜索框和表格样式修复")
    print("=" * 60)
    
    search_ok = check_search_section_styles()
    table_ok = check_table_styles()
    card_modal_ok = check_card_and_modal_styles()
    consistency_ok = analyze_color_consistency()
    
    print("\n" + "=" * 60)
    print("📊 验证结果")
    print("=" * 60)
    
    if search_ok and table_ok and card_modal_ok and consistency_ok:
        print("✅ 搜索框和表格样式修复完成！")
        print("\n🎨 修复的元素:")
        print("  • 🔍 搜索框: 玻璃质感背景，高级渐变边框")
        print("  • 📊 数据表格: 半透明背景，悬停动效")
        print("  • 🎴 卡片容器: 统一的渐变背景和阴影")
        print("  • 📝 模态框表单: 协调的输入框和按钮样式")
        print("  • 🎯 按钮系统: 统一的颜色和交互效果")
        print("\n💎 整体效果:")
        print("  • 颜色协调: 全站统一的高级配色方案")
        print("  • 视觉层次: 清晰的元素层次和深度感")
        print("  • 交互反馈: 丰富的悬停和点击动效")
        print("  • 现代质感: 玻璃效果和渐变背景")
        print("  • 专业形象: 精致的细节和高级的视觉体验")
        return True
    else:
        print("❌ 部分样式修复未正确应用，请检查上述错误")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
